package com.hntnbs.ai.survey.service.agent.loader;

import com.hntnbs.ai.survey.context.properties.ManusProperties;
import com.hntnbs.ai.survey.service.agent.base.LlmService;
import com.hntnbs.ai.survey.service.agent.fun_tools.ToolCallbackProvider;
import com.hntnbs.ai.survey.service.agent.reAct.ReActAgent;
import com.hntnbs.ai.survey.service.agent.recorders.PlanExecutionRecorder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.tool.ToolCallback;

import java.util.List;
import java.util.Map;

@Slf4j
public class DynamicAgent extends ReActAgent {


	/**
	 * 构造函数
	 *
	 * @param llmService            LLM服务实例，用于处理自然语言交互
	 * @param planExecutionRecorder 计划执行记录器，用于记录执行过程
	 * @param manusProperties       Manus配置属性
	 * @param initialAgentSetting
	 */
	public DynamicAgent(LlmService llmService, PlanExecutionRecorder planExecutionRecorder, ManusProperties manusProperties, Map<String, Object> initialAgentSetting) {
		super(llmService, planExecutionRecorder, manusProperties, initialAgentSetting);
	}

	public void setToolCallbackProvider(ToolCallbackProvider toolCallbackProvider) {
		this.toolCallbackProvider = toolCallbackProvider;
	}

	@Override
	protected boolean think() {
		return false;
	}

	@Override
	protected AgentExecResult act() {
		return null;
	}

	@Override
	public void clearUp(String planId) {

	}

	@Override
	public String getName() {
		return "";
	}

	@Override
	public String getDescription() {
		return "";
	}

	@Override
	protected Message getNextStepWithEnvMessage() {
		return null;
	}

	@Override
	public List<ToolCallback> getToolCallList() {
		return List.of();
	}
}